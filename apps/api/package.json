{"name": "@limico/api", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc -p tsconfig.json", "start": "node dist/index.js", "test": "vitest run", "test:watch": "vitest", "prisma": "prisma", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate"}, "dependencies": {"@prisma/client": "^5.16.1", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "zod": "^3.25.6"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^22.7.4", "prisma": "^5.16.1", "tsx": "^4.19.1", "typescript": "^5.5.4", "vitest": "^2.0.5"}}