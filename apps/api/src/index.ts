import 'dotenv/config';
import express from 'express';
import cors from 'cors';
import projects from './routes/projects';
import cases from './routes/cases';

const app = express();

app.use(cors());
app.use(express.json());

app.get('/health', (_req, res) => {
  res.json({ ok: true, env: process.env.NODE_ENV || 'development' });
});

app.use('/api/projects', projects);
app.use('/api/cases', cases);

const port = process.env.PORT || 4000;
app.listen(port, () => {
  console.log(`API listening on http://localhost:${port}`);
});

