export type DesignerStatus = '3d' | '2d' | 'pending_assign' | 'checklist' | 'furniture_list' | 'complete';

export const calculateDueDate = (
  status: DesignerStatus,
  salesAmount: number,
  isRevision: boolean = false
): string => {
  const today = new Date();
  let days = 0;

  const getTier = (amount: number) => {
    if (amount <= 30000) return 1;
    if (amount <= 50000) return 2;
    if (amount <= 80000) return 3;
    if (amount <= 100000) return 4;
    if (amount <= 120000) return 5;
    return 6; // 121k and above
  };

  const tier = getTier(salesAmount);

  if (status === '3d') {
    if (isRevision) {
      const revisionDays = [4, 4, 5, 5, 6, 6];
      days = revisionDays[tier - 1];
    } else {
      const firstDraftDays = [5, 6, 7, 8, 9, 10];
      days = firstDraftDays[tier - 1];
    }
  } else if (status === '2d') {
    if (isRevision) {
      const revisionDays = [4, 4, 5, 5, 6, 7];
      days = revisionDays[tier - 1];
    } else {
      const firstDraftDays = [7, 8, 9, 10, 11, 12];
      days = firstDraftDays[tier - 1];
    }
  } else {
    days = 7;
  }

  const dueDate = new Date(today);
  dueDate.setDate(today.getDate() + days);
  return dueDate.toISOString().split('T')[0];
};

