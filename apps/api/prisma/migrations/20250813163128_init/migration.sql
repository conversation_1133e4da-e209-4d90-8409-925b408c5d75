-- CreateTable
CREATE TABLE "Project" (
    "id" UUID NOT NULL,
    "title" TEXT NOT NULL,
    "client" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'lead',
    "salesAmount" INTEGER NOT NULL DEFAULT 0,
    "assignedTo" TEXT,
    "salesSubStatus" TEXT,
    "dueDate" TIMESTAMP(3),
    "expiredDate" TIMESTAMP(3),
    "revisions3d" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "revisions2d" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "isRevision" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Project_pkey" PRIMARY KEY ("id")
);
