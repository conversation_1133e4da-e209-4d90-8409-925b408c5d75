// Sales specific statuses
export type SalesStatus = 'lead' | 'quotation' | 'potential' | 'won_deal' | 'lost_deal' | 'completed';
export type SalesWonSubStatus = '10%' | '5%' | '45%' | '37%' | '3%';

// Designer specific statuses
export type DesignerStatus = 'designer_pending_assign' | 'checklist' | '3d' | '2d' | 'furniture_list' | 'complete' | 'designer_lost_deal';

// Site supervisor specific statuses
export type SupervisorStatus = 'supervisor_pending_assign' | 'inprogress' | 'completed' | 'supervisor_lost_deal';

// Site supervisor selectable small tasks (subtasks)
export type SupervisorSubTask = 'floor_protection' | 'plaster_ceiling' | 'spc' | 'first_painting' |
  'carpentry_measure' | 'measure_others' | 'carpentry_install' | 'quartz_measure' | 'quartz_install' |
  'glass_measure' | 'glass_install' | 'final_wiring' | 'final_painting' | 'install_others' |
  'plumbing' | 'cleaning' | 'defects';

export type ProjectStatus = SalesStatus | DesignerStatus | SupervisorStatus;
export type UserRole = 'sales' | 'designer' | 'supervisor' | 'manager';

export interface Task {
  id: string;
  caseId: string; // Links tasks across roles
  title: string;
  client: string;
  status: ProjectStatus;
  createdAt: string;
  updatedAt: string;
  assignedTo: string;
  roleType: UserRole;
  salesAmount?: number; // Total sales amount for tier calculation
  salesSubStatus?: SalesWonSubStatus; // For won deal sub-statuses
  completionProof?: string;
  parentTaskId?: string; // Reference to previous role's task
  dueDate?: string; // Due date for designer tasks
  expiredDate?: string; // Expired date for designer tasks
  revisions3d?: string[]; // Array of 3D design revision timestamps
  revisions2d?: string[]; // Array of 2D design revision timestamps
  isRevision?: boolean; // Whether task is in revision phase
}

// Backward compatibility - keeping Project interface
export interface Project {
  id: string;
  caseId?: string;
  parentTaskId?: string;
  title: string;
  client: string;
  status: ProjectStatus;
  createdAt: string;
  updatedAt: string;
  salesAmount?: number; // Total sales amount for tier calculation
  assignedTo?: string;
  completionProof?: string;
  salesSubStatus?: SalesWonSubStatus; // For won deal sub-statuses
  dueDate?: string; // Due date for designer tasks
  expiredDate?: string; // Expired date for designer tasks
  revisions3d?: string[]; // Array of 3D design revision timestamps
  revisions2d?: string[]; // Array of 2D design revision timestamps
  isRevision?: boolean; // Whether task is in revision phase
  supervisorSelectedPhases?: string[];
  supervisorPhaseDates?: Record<string, string>;
}

export interface User {
  id: string;
  name: string;
  role: UserRole;
  email: string;
}

export const SALES_STATUS_LABELS: Record<SalesStatus, string> = {
  lead: 'Lead',
  quotation: 'Quotation', 
  potential: 'Potential',
  won_deal: 'Won Deal',
  lost_deal: 'Lost Deal',
  completed: 'Completed'
};

export const SALES_WON_SUB_LABELS: Record<SalesWonSubStatus, string> = {
  '10%': '10% (Upon Engagement)',
  '5%': '5% (3D Visual Confirmation)',
  '45%': '45% (Upon Job Confirmation)',
  '37%': '37% (Upon Carpentry Materials Arrived Site)',
  '3%': '3% (Upon Job Completion)'
};

export const DESIGNER_STATUS_LABELS: Record<DesignerStatus, string> = {
  designer_pending_assign: 'Pending Assignment',
  checklist: 'Checklist',
  '3d': '3D Design',
  '2d': '2D Drawing',
  furniture_list: 'Furniture List',
  complete: 'Complete',
  designer_lost_deal: 'Lost Deal'
};

export const SUPERVISOR_STATUS_LABELS: Record<SupervisorStatus, string> = {
  supervisor_pending_assign: 'Pending Assignment',
  inprogress: 'In Progress',
  completed: 'Completed',
  supervisor_lost_deal: 'Lost Deal'
};

export const SUPERVISOR_SUBTASK_LABELS: Record<SupervisorSubTask, string> = {
  floor_protection: 'Floor Protection & First Stage Wiring',
  plaster_ceiling: 'Plaster Ceiling',
  spc: 'SPC',
  first_painting: 'First Layer Painting',
  carpentry_measure: 'Carpentry Measure',
  measure_others: 'Measure (Others)',
  carpentry_install: 'Carpentry Install',
  quartz_measure: 'Quartz Measure',
  quartz_install: 'Quartz Install',
  glass_measure: 'Glass Measure',
  glass_install: 'Glass Install',
  final_wiring: 'Final Stage Wiring',
  final_painting: 'Final Layer Painting',
  install_others: 'Install (Others)',
  plumbing: 'Plumbing',
  cleaning: 'Cleaning',
  defects: 'Defects'
};

export const ROLE_LABELS: Record<UserRole, string> = {
  sales: 'Sales Representative',
  designer: 'Designer',
  supervisor: 'Site Supervisor',
  manager: 'Manager'
};

// Legacy status labels for backward compatibility
export const PROJECT_STATUS_LABELS: Record<string, string> = {
  deposit: 'Awaiting Deposit',
  design: 'In Design',
  procurement: 'Procurement',
  installation: 'Installation',
  complete: 'Complete',
  ...SALES_STATUS_LABELS,
  ...DESIGNER_STATUS_LABELS,
  ...SUPERVISOR_STATUS_LABELS
};

// Helper functions for permissions
export const isManager = (role: UserRole): boolean => role === 'manager';

export const canModifyTask = (userRole: UserRole, taskAssignedTo: string, currentUserId: string): boolean => {
  // Managers can modify all tasks
  if (isManager(userRole)) {
    return true;
  }
  // Regular users can only modify their own tasks
  return taskAssignedTo === currentUserId;
};

export const getBaseRole = (role: UserRole): UserRole => {
  return role;
};

export const getStatusLabelsForRole = (role: UserRole) => {
  const baseRole = getBaseRole(role);
  switch (baseRole) {
    case 'sales':
      return SALES_STATUS_LABELS;
    case 'designer':
      return DESIGNER_STATUS_LABELS;
    case 'supervisor':
      return SUPERVISOR_STATUS_LABELS;
    default:
      return {};
  }
};

export const getStatusesForRole = (role: UserRole): ProjectStatus[] => {
  const baseRole = getBaseRole(role);
  switch (baseRole) {
    case 'sales':
      return Object.keys(SALES_STATUS_LABELS) as SalesStatus[];
    case 'designer':
      return Object.keys(DESIGNER_STATUS_LABELS) as DesignerStatus[];
    case 'supervisor':
      return Object.keys(SUPERVISOR_STATUS_LABELS) as SupervisorStatus[];
    default:
      return [];
  }
};

// Workflow progression logic
export const getNextRoleInWorkflow = (currentRole: UserRole): UserRole | null => {
  const baseRole = getBaseRole(currentRole);
  switch (baseRole) {
    case 'sales':
      return 'designer';
    case 'designer':
      return 'supervisor';
    case 'supervisor':
      return null; // End of workflow
    default:
      return null;
  }
};

export const shouldCreateNextTask = (role: UserRole, status: ProjectStatus, subStatus?: SalesWonSubStatus): boolean => {
  const baseRole = getBaseRole(role);
  if (baseRole === 'sales' && status === 'won_deal' && subStatus === '10%') {
    return true; // Create designer task when sales reaches won_deal 10%
  }
  if (baseRole === 'designer' && status === 'complete') {
    return true; // Create supervisor task when design is complete
  }
  return false;
};

// Check if a case is fully completed (all three roles completed)
export const isCaseCompleted = (projects: Project[], caseTitle: string, caseClient: string): boolean => {
  const relatedProjects = projects.filter(p => p.title === caseTitle && p.client === caseClient);
  
  const salesCompleted = relatedProjects.some(p => {
    const assignedUser = p.assignedTo;
    // Assuming we can determine role from assigned user - would need user lookup in real implementation
    return p.status === 'completed';
  });
  
  const designerCompleted = relatedProjects.some(p => p.status === 'complete');
  const supervisorCompleted = relatedProjects.some(p => p.status === 'completed');
  
  return salesCompleted && designerCompleted && supervisorCompleted;
};

// Get the next status for progression, including completion logic
export const getNextStatusInProgression = (currentStatus: ProjectStatus, role: UserRole, subStatus?: SalesWonSubStatus): ProjectStatus | null => {
  const baseRole = getBaseRole(role);
  if (baseRole === 'sales') {
    if (currentStatus === 'won_deal' && subStatus === '3%') {
      return 'completed'; // After 3%, sales task becomes completed
    }
    // Handle other sales progressions
    const salesStatuses: SalesStatus[] = ['lead', 'quotation', 'potential', 'won_deal'];
    const currentIndex = salesStatuses.indexOf(currentStatus as SalesStatus);
    if (currentIndex >= 0 && currentIndex < salesStatuses.length - 1) {
      return salesStatuses[currentIndex + 1];
    }
  }
  
  if (baseRole === 'designer') {
    const designerStatuses: DesignerStatus[] = ['designer_pending_assign', 'checklist', '3d', '2d', 'furniture_list', 'complete'];
    const currentIndex = designerStatuses.indexOf(currentStatus as DesignerStatus);
    if (currentIndex >= 0 && currentIndex < designerStatuses.length - 1) {
      return designerStatuses[currentIndex + 1];
    }
  }
  
  if (baseRole === 'supervisor') {
    // Supervisor status does not auto-progress via linear phases. Subtasks drive completion, and
    // status remains 'inprogress' until all selected subtasks are completed (then backend sets 'completed').
    return null;
  }
  
  return null;
};