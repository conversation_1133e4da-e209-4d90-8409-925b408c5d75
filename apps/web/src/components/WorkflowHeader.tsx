import { User, UserRole, R<PERSON><PERSON>_LABELS, getBaseRole } from "@/types/project";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Building2, UserCircle, BarChart3, FolderKanban } from "lucide-react";
import { useNavigate, useLocation } from "react-router-dom";

interface WorkflowHeaderProps {
  currentUser: User;
  currentView: UserRole;
  onRoleChange: (role: UserRole) => void;
  onUserChange: (user: User) => void;
  availableUsers: User[];
}

export const WorkflowHeader = ({ currentUser, currentView, onRoleChange, onUserChange, availableUsers }: WorkflowHeaderProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  // Only show base roles in view switcher
  const viewRoles: UserRole[] = ['sales', 'designer', 'supervisor'];

  const getRoleBadgeVariant = (role: UserRole) => {
    switch (role) {
      case 'sales':
        return 'bg-role-sales text-white';
      case 'designer':
        return 'bg-role-designer text-white';
      case 'supervisor':
        return 'bg-role-supervisor text-white';
      case 'manager':
        return 'secondary';
      default: return 'secondary';
    }
  };

  return (
    <header className="border-b bg-card shadow-sm">
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-3">
              <Building2 className="h-8 w-8 text-primary" />
              <div>
                <h1 className="text-2xl font-bold text-foreground">Workflow Manager</h1>
                <p className="text-sm text-muted-foreground">Project Management System</p>
              </div>
            </div>

            <nav className="flex items-center gap-2">
              <Button
                variant={location.pathname === '/' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => navigate('/')}
                className="flex items-center gap-2"
              >
                <FolderKanban className="h-4 w-4" />
                Projects
              </Button>
            </nav>

                <Button
                  variant={location.pathname === '/case-history' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => navigate('/case-history')}
                  className="flex items-center gap-2"
                >
                  <BarChart3 className="h-4 w-4" />
                  Case History
                </Button>

          </div>

          <div className="flex items-center gap-4">
            {/* User Login Mock Dropdown */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Login as:</span>
              <Select
                value={currentUser.id}
                onValueChange={(userId) => {
                  const user = availableUsers.find(u => u.id === userId);
                  if (user) onUserChange(user);
                }}
              >
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {availableUsers.map((user) => (
                    <SelectItem key={user.id} value={user.id}>
                      <div className="flex items-center gap-2">
                        <span>{user.name}</span>
                        <Badge variant="outline" className="text-xs">
                          {ROLE_LABELS[user.role]}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <UserCircle className="h-5 w-5 text-muted-foreground" />
              <span className="text-sm font-medium">{currentUser.name}</span>
              <Badge className={getRoleBadgeVariant(currentUser.role)}>
                {ROLE_LABELS[currentUser.role]}
              </Badge>
            </div>

            <div className="flex gap-1 bg-muted p-1 rounded-lg">
              <span className="text-xs text-muted-foreground px-2 py-1">View:</span>
              {viewRoles.map((role) => (
                <Button
                  key={role}
                  variant={currentView === role ? "default" : "ghost"}
                  size="sm"
                  onClick={() => onRoleChange(role)}
                  className="text-xs px-3"
                >
                  {ROLE_LABELS[role]}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};