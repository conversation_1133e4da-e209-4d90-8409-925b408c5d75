import { useState } from 'react';
import { User, SUPERVISOR_SUBTASK_LABELS } from '@/types/project';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';

const SUPERVISOR_PHASES = [
  'floor_protection', 'plaster_ceiling', 'spc', 'first_painting',
  'carpentry_measure', 'measure_others', 'carpentry_install', 'quartz_measure', 'quartz_install',
  'glass_measure', 'glass_install', 'final_wiring', 'final_painting', 'install_others',
  'plumbing', 'cleaning', 'defects'
] as const;

export function SupervisorAssignDialog({
  open,
  onOpenChange,
  designers,
  onConfirm,
}: {
  open: boolean;
  onOpenChange: (v: boolean) => void;
  designers: User[];
  onConfirm: (assigneeId: string, phases: string[]) => void;
}) {
  const [assignee, setAssignee] = useState('');
  const [selected, setSelected] = useState<string[]>([]);

  const toggle = (phase: string) => {
    setSelected((prev) => prev.includes(phase) ? prev.filter(p => p !== phase) : [...prev, phase]);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Assign Supervisor Task</DialogTitle>
        </DialogHeader>
        <div className="space-y-3">
          <div>
            <label className="text-sm">Assign to</label>
            <Select value={assignee} onValueChange={setAssignee}>
              <SelectTrigger>
                <SelectValue placeholder="Select a supervisor" />
              </SelectTrigger>
              <SelectContent>
                {designers.map((u) => (
                  <SelectItem key={u.id} value={u.id}>{u.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <label className="text-sm">Select tasks</label>
            <div className="grid grid-cols-2 gap-2 max-h-64 overflow-auto mt-2">
              {SUPERVISOR_PHASES.map((phase) => (
                <label key={phase} className="flex items-center gap-2 text-sm">
                  <Checkbox checked={selected.includes(phase)} onCheckedChange={() => toggle(phase)} />
                  <span>{(SUPERVISOR_SUBTASK_LABELS as Record<string, string>)[phase]}</span>
                </label>
              ))}
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
            <Button disabled={!assignee || selected.length === 0} onClick={() => { onConfirm(assignee, selected); onOpenChange(false); }}>Assign</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

